# StockTracker License

Copyright (c) 2025 StockTracker Contributors

## Educational Use License

This project is licensed for educational and research purposes only.

### Permitted Use

You are free to:
- Use this software for learning purposes
- Study the code to understand stock analysis and prediction techniques
- Modify the code for educational purposes
- Share the code with others for educational purposes
- Use the code in academic research projects

### Restrictions

You may NOT:
- Use this software for commercial purposes
- Use this software for actual trading or investment decisions
- Distribute this software as part of a commercial product
- Remove or modify this license notice
- Use this software in any way that could result in financial loss

### Disclaimer

This software is provided "as is" for educational purposes only. The authors and contributors make no warranties, express or implied, including but not limited to the warranties of merchantability, fitness for a particular purpose, and non-infringement.

The predictions and analysis generated by this software should not be used for actual trading or investment decisions. Stock markets are inherently unpredictable, and any predictions made by machine learning models may be inaccurate.

In no event shall the authors or copyright holders be liable for any claim, damages, or other liability, whether in an action of contract, tort, or otherwise, arising from, out of, or in connection with the software or the use or other dealings in the software.

### Third-Party Licenses

This project uses several third-party libraries that are subject to their own licenses:
- akshare - MIT License
- tensorflow - Apache License 2.0
- scikit-learn - BSD 3-Clause License
- matplotlib - PSF License
- xgboost - Apache License 2.0
- joblib - BSD 3-Clause License
- plotly - MIT License
- streamlit - Apache License 2.0

Please consult the respective licenses of these libraries for their terms of use.

### Contact

For questions about this license, please contact the project maintainers.