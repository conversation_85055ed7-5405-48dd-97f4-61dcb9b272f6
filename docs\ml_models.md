# 机器学习模型文档

StockTracker 使用多种先进的机器学习模型进行股票价格预测，每种模型都有其独特的优势和适用场景。本文档详细介绍了项目中实现的各个模型，包括其工作原理、架构、参数设置和训练过程。

## 1. LSTM (长短期记忆网络)

### 工作原理

LSTM（Long Short-Term Memory）是一种特殊的循环神经网络（RNN），专门设计用于解决传统RNN在处理长序列数据时遇到的梯度消失或梯度爆炸问题。LSTM通过引入门控机制来控制信息的流动，能够有效地学习长期依赖关系。

LSTM的核心组件包括：
- **遗忘门（Forget Gate）**：决定从细胞状态中丢弃什么信息
- **输入门（Input Gate）**：决定哪些新信息将被存储在细胞状态中
- **输出门（Output Gate）**：决定输出什么内容

### 项目中的架构

在StockTracker中，LSTM模型采用以下架构：

```python
Sequential()
├── LSTM(units=50, return_sequences=True, input_shape=(look_back, 1))
├── Dropout(0.2)
├── LSTM(units=50, return_sequences=True)
├── Dropout(0.2)
├── LSTM(units=50)
├── Dropout(0.2)
└── Dense(units=1)
```

其中：
- `look_back`：默认为60，表示使用过去60天的历史数据进行预测
- 第一层LSTM返回完整的序列输出以供下一层处理
- 每层LSTM后都有0.2的Dropout层用于防止过拟合
- 最后一层全连接层输出单个预测值

### 参数和超参数

主要参数设置：
- **输入维度**：(60, 1) - 使用过去60天的收盘价数据
- **隐藏层单元数**：每层50个单元
- **Dropout率**：0.2
- **优化器**：Adam
- **损失函数**：均方误差（MSE）
- **批次大小**：32
- **训练轮数**：50轮

### 训练过程

1. 数据预处理：使用MinMaxScaler将收盘价数据缩放到[0,1]范围
2. 数据分割：将数据按8:2比例分为训练集和测试集
3. 序列构建：将时间序列数据重构为监督学习格式
4. 模型编译：使用Adam优化器和均方误差损失函数
5. 模型训练：在训练集上训练50个epoch，使用测试集进行验证

## 2. GRU (门控循环单元)

### 与LSTM的差异

GRU（Gated Recurrent Unit）是LSTM的一种简化变体，具有以下特点：
- 只有两个门：重置门（Reset Gate）和更新门（Update Gate）
- 没有单独的细胞状态，隐藏状态同时承担了细胞状态的功能
- 参数更少，计算效率更高
- 在许多任务中性能与LSTM相当甚至更好

### 项目中的实现

GRU模型在StockTracker中的实现与LSTM类似，但使用GRU层替代LSTM层：

```python
Sequential()
├── GRU(units=50, return_sequences=True, input_shape=(look_back, 1))
├── Dropout(0.2)
├── GRU(units=50, return_sequences=True)
├── Dropout(0.2)
├── GRU(units=50)
├── Dropout(0.2)
└── Dense(units=1)
```

### 优势和劣势

**优势**：
- 计算复杂度较低，训练速度更快
- 参数更少，有助于减少过拟合风险
- 在某些时间序列预测任务中表现良好

**劣势**：
- 对于非常长的序列，可能不如LSTM有效
- 缺少独立的细胞状态可能影响长期记忆能力

## 3. Transformer 模型

### 注意力机制解释

Transformer模型基于注意力机制，完全摒弃了传统的循环结构。其核心是自注意力（Self-Attention）机制，能够直接计算序列中任意两个位置之间的依赖关系，无论它们之间的距离有多远。

注意力机制的计算过程：
1. 对输入序列计算Query、Key和Value向量
2. 通过Query和Key的点积计算注意力分数
3. 对注意力分数进行Softmax归一化
4. 使用归一化后的分数对Value进行加权求和

### 项目中的实现细节

在StockTracker中，Transformer模型的实现较为简化：

```python
Input(shape=(look_back,))
├── Dense(64, activation='relu')
├── Dropout(0.2)
├── Dense(32, activation='relu')
├── Dropout(0.2)
├── Dense(16, activation='relu')
├── Dropout(0.2)
└── Dense(1)
```

注意：项目中当前实现的是一个简化的前馈神经网络，而非完整的Transformer架构。完整的Transformer实现会包含多头注意力机制和位置编码等组件。

### 适用性分析

对于股票预测任务，Transformer的优势包括：
- 能够捕捉长期依赖关系
- 并行化程度高，训练效率好
- 对序列中任意位置的依赖关系建模能力强

但在当前实现中，由于使用了简化的架构，可能无法充分发挥Transformer的优势。

## 4. 随机森林

### 集成学习方法

随机森林是一种基于决策树的集成学习方法，通过构建多个决策树并将它们的预测结果进行组合来提高模型的准确性和稳定性。

工作原理：
1. 从训练数据中进行自助采样（Bootstrap Sampling）生成多个子数据集
2. 在每个子数据集上训练一个决策树，且在每个节点分裂时只考虑特征的一个随机子集
3. 对于回归任务，将所有树的预测结果取平均作为最终预测

### 特征重要性

在股票预测中，随机森林可以提供特征重要性信息，帮助理解哪些技术指标或特征对预测结果影响最大。StockTracker中使用的特征包括：
- 价格变化和价格变化百分比
- 高低价差比率、开盘收盘价差比率
- 不同周期的移动平均线（5日、10日、20日）
- 波动率（10日标准差）
- 时间特征（星期几、月份、季度）

### 实现细节

StockTracker中的随机森林模型参数设置：
- 树的数量（n_estimators）：100
- 最大深度（max_depth）：10
- 随机种子（random_state）：42
- 并行处理（n_jobs）：-1（使用所有可用CPU核心）

## 5. XGBoost

### 梯度提升技术

XGBoost（Extreme Gradient Boosting）是一种高效的梯度提升框架，通过迭代地添加新模型来纠正前一轮模型的错误。

工作原理：
1. 初始化模型（通常为常数值）
2. 计算当前模型的残差（真实值与预测值之差）
3. 训练一个新的弱学习器（通常是决策树）来拟合这些残差
4. 将新学习器以一定权重添加到现有模型中
5. 重复步骤2-4直到满足停止条件

### 性能特点

XGBoost在StockTracker中的优势：
- 计算效率高，训练速度快
- 内置正则化项，有助于防止过拟合
- 支持并行处理
- 对缺失值具有良好的处理能力
- 提供丰富的参数调节选项

### 超参数调优

StockTracker中XGBoost的默认参数：
- 树的数量（n_estimators）：100
- 最大深度（max_depth）：6
- 学习率（learning_rate）：0.1
- 随机种子（random_state）：42

在实际应用中，可通过网格搜索或贝叶斯优化等方法进一步调优这些参数。

## 6. 模型比较

### 性能基准

在StockTracker中，可以通过`compare_models`函数对不同模型进行性能比较。评估指标包括：
- MAE（平均绝对误差）
- RMSE（均方根误差）
- MAPE（平均绝对百分比误差）

### 适用场景

不同模型在不同场景下的表现：

**LSTM/GRU**：
- 适用于具有明显时间依赖性的序列数据
- 在历史模式较为稳定的股票中表现较好
- 需要较大量的训练数据

**Transformer**：
- 理论上适合捕捉长期依赖关系
- 在数据量充足且序列较长时表现优异
- 计算资源需求较高

**随机森林**：
- 对特征工程要求较高，但解释性强
- 在特征与目标变量存在非线性关系时表现良好
- 不容易过拟合，对异常值相对鲁棒

**XGBoost**：
- 在结构化数据上通常表现优秀
- 训练速度相对较快
- 对特征缩放不敏感

### 准确性考虑

在股票预测任务中，需要注意：
1. 股票市场受多种复杂因素影响，任何模型都无法保证100%准确
2. 短期预测通常比长期预测更准确
3. 模型性能可能随市场环境变化而变化
4. 应结合多种模型和指标进行综合判断

## 7. 特征工程

### 使用的技术指标

StockTracker在特征工程中使用了多种技术指标：
- **移动平均线**：简单移动平均线（SMA）和指数移动平均线（EMA）
- **相对强弱指数（RSI）**：衡量股票超买或超卖状态
- **异同移动平均线（MACD）**：判断买卖时机的技术指标
- **布林带**：衡量股价波动性和超买超卖状态
- **随机指标（Stochastic Oscillator）**：比较收盘价与一定时期内的价格范围
- **能量潮指标（OBV）**：衡量资金流向
- **成交量加权平均价格（VWAP）**：衡量平均交易价格
- **蔡金资金流量指标（CMF）**：衡量资金流入流出情况

### 时间序列特征

除了价格和技术指标外，还包含了时间相关的特征：
- 星期几（day_of_week）：某些股票可能在特定星期表现不同
- 月份（month）：季节性因素对某些行业股票的影响
- 季度（quarter）：季度财报等因素的影响

### 数据预处理步骤

1. **数据清洗**：移除缺失值和异常值
2. **特征缩放**：使用MinMaxScaler将特征缩放到[0,1]范围
3. **特征选择**：根据可用数据选择合适的特征
4. **时间序列重构**：将一维时间序列重构为监督学习格式
5. **数据分割**：按时间顺序将数据分为训练集和测试集

通过这些特征工程步骤，模型能够更好地理解和学习股票价格的变化规律，从而提高预测准确性。