# 常见问题解答 (FAQ)

## 目录
1. [通用问题](#通用问题)
2. [安装与设置](#安装与设置)
3. [数据与来源](#数据与来源)
4. [模型与预测](#模型与预测)
5. [Web界面](#web界面)
6. [技术问题](#技术问题)

---

## 通用问题

### 什么是StockTracker？
StockTracker 是一个基于机器学习的股票价格预测系统。它使用 akshare 获取股票数据，支持多种先进的机器学习模型进行预测，包括 LSTM、GRU、Transformer 以及集成学习模型（随机森林、XGBoost）。

### StockTracker适合用于实际交易吗？
StockTracker 仅供学习和研究使用，不构成投资建议。虽然系统提供了多种预测模型和分析工具，但股票市场具有高度不确定性，机器学习模型的预测结果可能存在误差。投资有风险，入市需谨慎。

### 系统要求是什么？
- 操作系统：Windows、macOS 或 Linux
- Python 3.12 或更高版本
- 至少 4GB RAM（推荐 8GB 或更多）
- 至少 2GB 可用磁盘空间

### 需要哪个Python版本？
StockTracker 需要 Python 3.12 或更高版本。项目使用 `uv` 作为包管理器，依赖项已在 `pyproject.toml` 中配置。

---

## 安装与设置

### 如何安装StockTracker？
1. 克隆项目：
   ```bash
   git clone https://github.com/your-username/StockTracker.git
   cd StockTracker
   ```
2. 安装依赖：
   项目使用 `uv` 作为包管理器，依赖项已在 `pyproject.toml` 中配置。
   ```bash
   uv sync
   ```
3. 验证安装：
   运行主程序，查看是否能正常输出帮助信息：
   ```bash
   python main.py --help
   ```

### 如果 uv 不可用怎么办？
如果未安装 `uv`，请先使用 `pip` 安装：
```bash
pip install uv
```
然后按照正常安装步骤继续。

### 可以使用 pip 而不是 uv 吗？
虽然推荐使用 `uv` 作为包管理器，但您也可以使用 `pip` 来安装依赖。在项目目录中运行：
```bash
pip install -r requirements.txt
```

### 如何更新项目？
1. 更新代码：
   ```bash
   git pull
   ```
2. 更新依赖：
   ```bash
   uv sync
   ```

---

## 数据与来源

### 股票数据来自哪里？
StockTracker 使用 akshare 库获取实时股票数据。akshare 是一个基于 Python 的开源金融数据接口库，提供了丰富的中国股票市场数据。

### 数据多久更新一次？
数据更新频率取决于 akshare 的数据源。一般来说，历史数据是静态的，而实时数据会根据市场交易时间进行更新。

### 可以使用自己的数据吗？
是的，您可以使用自己的数据。您需要按照项目的数据格式要求来准备数据，并修改 `data/fetcher.py` 文件以支持您的数据源。

### 支持哪些股票市场？
StockTracker 主要支持中国 A 股市场，但通过 akshare 的扩展能力，理论上可以支持全球多个股票市场。具体支持的市场请参考 akshare 的官方文档。

---

## 模型与预测

### 哪个模型最准确？
不同的模型在不同的市场环境和股票上表现可能不同。一般来说：
- LSTM 和 GRU 模型适合处理时间序列数据
- Transformer 模型在处理长序列时表现较好
- 随机森林和 XGBoost 在处理结构化特征时表现良好

建议您根据具体需求和数据特点选择合适的模型，并通过回测来验证模型性能。

### 模型能预测多远的未来？
默认情况下，模型可以预测未来 30 天的股票价格走势。您可以根据需要调整预测天数，但需要注意预测时间越长，误差可能越大。

### 应该多久重新训练一次模型？
建议根据市场变化情况定期重新训练模型：
- 短期预测（1-7天）：每周重新训练
- 中期预测（1-30天）：每月重新训练
- 长期预测（30天以上）：每季度重新训练

### 可以添加自定义模型吗？
是的，您可以添加自定义模型。您需要：
1. 在 `models/` 目录下创建新的模型文件
2. 实现模型的训练和预测接口
3. 在 `models/predictors.py` 中注册您的模型

---

## Web界面

### 如何访问Web界面？
在项目目录中运行以下命令启动Web界面：
```bash
streamlit run app.py
```
启动后，Web界面将在默认浏览器中打开，通常地址为 http://localhost:8501

### 可以自定义仪表板吗？
是的，您可以自定义仪表板。Web界面基于 Streamlit 构建，您可以通过修改 `ui/web.py` 文件来调整界面布局和功能。

### 如何导出结果？
在Web界面中，您可以：
1. 使用图表的导出功能保存图片
2. 复制数据表格到剪贴板
3. 通过代码接口导出数据到文件

### 可以在服务器上运行吗？
是的，您可以在服务器上运行Web界面。请注意：
1. 确保服务器满足系统要求
2. 配置防火墙以允许访问Web端口
3. 考虑使用 nginx 等反向代理来提高安全性

---

## 技术问题

### 为什么预测有时是错误的？
股票价格预测受多种因素影响，包括：
- 市场情绪变化
- 突发事件影响
- 数据质量问题
- 模型局限性

机器学习模型只能基于历史数据学习模式，无法预测所有影响因素。

### 如何提高预测准确性？
您可以尝试以下方法提高预测准确性：
1. 使用更多特征（技术指标、基本面数据等）
2. 调整模型超参数
3. 使用集成学习方法
4. 增加训练数据量
5. 定期重新训练模型

### 风险指标是什么意思？
StockTracker 提供多种风险指标：
- **波动率（标准差）**：衡量价格变动的剧烈程度
- **VaR（风险价值）**：在一定置信水平下可能的最大损失
- **最大回撤**：投资期间从峰值到谷值的最大跌幅
- **夏普比率**：风险调整后的收益率
- **贝塔系数**：相对于市场指数的波动性

### 如何解读图表？
StockTracker 提供多种图表：
- **K线图**：显示开盘价、收盘价、最高价、最低价
- **技术指标图**：显示各种技术指标的变化趋势
- **预测图**：显示历史价格和预测价格
- **风险指标图**：可视化各种风险指标