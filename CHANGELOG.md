# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2025-07-25

### Added

- Stock price prediction with machine learning models:
  - LSTM (Long Short-Term Memory)
  - GRU (Gated Recurrent Unit)
  - Transformer
  - Random Forest
  - XGBoost
- Technical indicator calculations
- Risk assessment capabilities:
  - Volatility calculation
  - VaR (Value at Risk)
  - Maximum drawdown calculation
  - Sharpe ratio calculation
  - Beta coefficient calculation
  - Alpha value calculation
- Portfolio analysis features:
  - Portfolio construction and weight allocation
  - Expected return and risk calculation
  - Modern Portfolio Theory (MPT) calculations
  - Capital Asset Pricing Model (CAPM) calculations
  - Portfolio optimization algorithms
- Strategy backtesting functionality
- Advanced visualization capabilities:
  - Interactive charts using Plotly
  - Candlestick charts
  - Technical indicator overlay charts
  - Heatmaps
  - 3D visualizations
  - Animated charts
  - Confidence interval visualization
  - Model performance comparison charts
  - Risk metrics visualization
  - Real-time data visualization
  - Comprehensive dashboard